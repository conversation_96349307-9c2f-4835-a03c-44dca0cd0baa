# Tech Stack & Architecture Rules

## Framework & Platform
- **React Native 0.76.9** with **Expo SDK 52** (managed workflow)
- **TypeScript** (strict mode) for type safety
- **Expo Dev Client** for custom native code integration

## State Management & Architecture
- **MobX-State-Tree (MST)** for application state management
- **MobX React Lite** for reactive components
- **Service layer pattern** for business logic separation (MANDATORY)
- **Pure UI Components** - NO business logic in components

## Navigation & UI
- **React Navigation 7** (Native Stack Navigator)
- **React Native Gesture Handler** for gestures
- **React Native Reanimated 3** for animations
- **Gorhom Bottom Sheet** for modal presentations
- **React Native Safe Area Context** for safe area handling
- **ThemedStyle** system for consistent styling

## Database & Storage
- **Expo SQLite** with **Drizzle ORM** for local database
- **React Native MMKV** for fast key-value storage
- **Supabase** for backend services

## Development Tools
- **Reactotron** for debugging (dev only)
- **ESLint** + **Prettier** for code formatting
- **Jest** + **React Native Testing Library** for unit testing
- **Maestro** for E2E testing

## Build & Deployment
- **EAS Build** for cloud builds
- **EAS Deploy** for web deployment

## MANDATORY Architecture Patterns

### Component Pattern (STRICT)
```typescript
import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface ComponentNameProps {
  // Props definition
}

export const ComponentName: FC<ComponentNameProps> = observer(function ComponentName(props) {
  const { themed } = useAppTheme()
  
  return (
    <View style={themed($container)}>
      {/* Component JSX */}
    </View>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  // Themed styles here
})
```

### Service Layer Pattern (MANDATORY)
```typescript
export class FeatureService {
  static businessLogicMethod(data: DataType): ResultType {
    // All business logic, calculations, validations here
    // NO React or React Native imports allowed
  }
}
```

### Hook Separation (MANDATORY)
- `useFeatureData()` - ONLY data fetching and state
- `useFeatureActions()` - ONLY business logic and user interactions
- `useFeatureNavigation()` - ONLY navigation functions
- `useFeatureUIActions()` - ONLY UI state and behaviors

## Common Commands

### Development
```bash
npm start                    # Start Expo dev server
npm run android             # Run on Android
npm run ios                 # Run on iOS
npm run web                 # Run on web
```

### Code Quality
```bash
npm run lint                # Run ESLint with auto-fix
npm run lint:check          # Check linting without fixing
npm run compile             # TypeScript compilation check
npm test                    # Run Jest tests
npm run test:watch          # Run tests in watch mode
```

### Database
```bash
npm run studio              # Open Drizzle Studio
npm run drizzle:generate    # Generate database migrations
```

### Building
```bash
npm run build:ios:sim       # Build iOS simulator
npm run build:android:sim   # Build Android simulator
npm run build:ios:dev       # Build iOS development
npm run build:android:dev   # Build Android development
```

### Testing
```bash
npm run test:maestro        # Run Maestro E2E tests (dev)
npm run test:maestro:ci     # Run Maestro E2E tests (CI)
```

## Node Version
- Requires Node.js ^18.18.0 || >=20.0.0