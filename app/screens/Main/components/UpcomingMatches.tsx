import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { SectionCard, UpcomingMatchCard, UpcomingMatch } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

interface UpcomingMatchesProps {
  upcomingMatches: UpcomingMatch[]
  onMatchPress: (match: UpcomingMatch) => void
}

export const UpcomingMatches: FC<UpcomingMatchesProps> = ({
  upcomingMatches,
  onMatchPress,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($upcomingMatchesContainer)}>
      <SectionCard
        title="Your Upcoming Matches"
        containerStyle={themed($upcomingMatchesSection)}
        contentStyle={themed($upcomingMatchesContent)}
        titleStyle={themed($upcomingMatchesTitle)}
      >
        {upcomingMatches.map((match) => (
          <UpcomingMatchCard key={match.id} match={match} onPress={onMatchPress} />
        ))}
      </SectionCard>
    </View>
  )
}

const $upcomingMatchesContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  // Removed paddingHorizontal (left/right margins)
  paddingTop: spacing.sm,
})

const $upcomingMatchesSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
})

const $upcomingMatchesContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "transparent",
  borderRadius: spacing.md,
  paddingTop: spacing.xs, // Reduced from spacing.md to spacing.xs
  // Removed paddingHorizontal (left/right margins)
  paddingBottom: spacing.sm,
  borderWidth: 0,
  shadowOpacity: 0,
  elevation: 0,
})

const $upcomingMatchesTitle: ThemedStyle<any> = ({ colors, spacing }) => ({
  color: colors.text,
  fontSize: 18,
  fontWeight: "600",
  paddingHorizontal: spacing.lg, // Add horizontal padding to title
})