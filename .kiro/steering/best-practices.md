# Best Practices & Performance

## 🏆 **Project Strengths - Leverage These Patterns**

Our codebase has been built with excellent architectural foundations. **USE THESE EXISTING PATTERNS** instead of reinventing the wheel:

### ✅ **Theming System** - `useAppTheme` Hook
**Location**: `app/utils/useAppTheme.ts`
```typescript
// ✅ ALWAYS use the theming system for consistent styling
import { useAppTheme } from "@/utils/useAppTheme"

const MyComponent = () => {
  const { themed, theme, themeScheme } = useAppTheme()
  
  const $container = themed(({ colors, spacing }) => ({
    backgroundColor: colors.background,
    padding: spacing.md,
  }))
  
  return <View style={$container}>...</View>
}
```

### ✅ **Icon System** - Centralized Icon Management
**Location**: `app/components/Icon.tsx`
```typescript
// ✅ Use the centralized Icon component
import { Icon } from "@/components"

<Icon icon="back" size={24} color={colors.text} />
<Icon icon="bell" onPress={handleNotification} />
```
**Available Icons**: See `assets/icons/` directory for all available icons

### ✅ **Component Preset System** - Smart Component Variants
**Examples in**: `app/components/Button.tsx`, `app/components/Text.tsx`, `app/components/Screen.tsx`
```typescript
// ✅ Leverage preset patterns for consistent UI
<Button preset="filled" text="Primary Action" />
<Button preset="reversed" text="Secondary Action" />
<Text preset="heading" text="Page Title" />
<Screen preset="scroll" safeAreaEdges={["top"]} />
```

### ✅ **MobX-State-Tree** - Predictable State Management
**Location**: `app/models/RootStore.ts`, `app/models/AuthenticationStore.ts`
```typescript
// ✅ Use MST stores for global state
import { useStores } from "@/models"

const MyComponent = observer(() => {
  const { authenticationStore } = useStores()
  
  return (
    <Text>{authenticationStore.isAuthenticated ? "Logged In" : "Not Logged In"}</Text>
  )
})
```

### ✅ **Type-Safe Navigation** - React Navigation Best Practices
**Location**: `app/navigators/AppNavigator.tsx`
```typescript
// ✅ Use typed navigation throughout the app
import { AppStackScreenProps } from "@/navigators"

interface ScreenProps extends AppStackScreenProps<"ScreenName"> {}

const MyScreen: FC<ScreenProps> = ({ navigation, route }) => {
  // Type-safe navigation parameters
  const { userId } = route.params
  
  // Type-safe navigation calls
  navigation.navigate("Profile", { userId: 123 })
}
```

### ✅ **Internationalization** - i18n Ready System
**Location**: `app/i18n/` directory
```typescript
// ✅ Use translation keys for all user-facing text
import { TxKeyPath, translate } from "@/i18n"

const welcomeText = translate("welcomeScreen.title")
// Supports multiple languages: en, es, fr, ar, hi, ja, ko
```

### ✅ **Safe Area Handling** - Mobile-Optimized Layout
**Location**: `app/utils/useSafeAreaInsetsStyle.ts`
```typescript
// ✅ Always handle device safe areas properly
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"

const $containerStyle = useSafeAreaInsetsStyle(["top", "bottom"])
```

## 🏗️ **Component Structure Rules**

### File Size Limits
- **Main components**: 100-300 lines maximum
- **Sub-components**: 50-150 lines maximum  
- **Custom hooks**: 20-100 lines maximum
- **Utility functions**: Extract to separate files when > 50 lines

### Single Responsibility Principle
- Each component should have ONE clear purpose
- If a component handles multiple concerns, split it into smaller components
- Use composition over inheritance

## 📝 **TypeScript Rules**

### Type Definitions
- **ALWAYS** define interfaces for props, state, and data structures
- Use `interface` for object shapes, `type` for unions/intersections
- Export types that are used by multiple components
- Use generics for reusable components

```typescript
// ✅ Good
interface EventCardProps {
  event: Event;
  onEventClick: (eventId: number) => void;
  isJoined?: boolean;
}

// ❌ Bad - no type definitions
const EventCard = ({ event, onEventClick, isJoined }) => {
```

### Naming Conventions
- **PascalCase**: Components, interfaces, types
- **camelCase**: variables, functions, hooks
- **SCREAMING_SNAKE_CASE**: constants
- **kebab-case**: file names (except components)

## ⚛️ **React Best Practices**

### Component Design
- Use functional components with hooks
- Prefer composition over complex prop interfaces
- Extract custom hooks for complex state logic
- Use `React.memo()` for expensive components

### State Management
- Keep state as close to where it's used as possible
- Use context sparingly - only for truly global state
- Extract complex state logic to custom hooks
- Use `useState` for simple state, `useReducer` for complex state

### Performance
- Use `useMemo()` for expensive calculations
- Use `useCallback()` for functions passed as props
- Implement lazy loading for large components
- Avoid creating objects/functions in render

```typescript
// ✅ Good
const memoizedValue = useMemo(() => expensiveCalculation(data), [data]);
const handleClick = useCallback((id: number) => {
  onItemClick(id);
}, [onItemClick]);

// ❌ Bad
const value = expensiveCalculation(data); // Runs on every render
const handleClick = (id: number) => onItemClick(id); // New function on every render
```

## 🎯 **Component Extraction Rules**

### When to Extract a Component
1. **Reusability**: Used in 2+ places
2. **Complexity**: > 50 lines of JSX
3. **Responsibility**: Handles a distinct concern
4. **Testing**: Needs independent testing

### Custom Hooks
- Prefix with `use`
- Return objects for multiple values
- Keep hooks focused on single concerns
- Extract side effects from components

```typescript
// ✅ Good
const useEventManagement = (communityId: string) => {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(false);
  
  const createEvent = useCallback(async (eventData: CreateEventData) => {
    // logic here
  }, []);
  
  return { events, loading, createEvent };
};
```

## 🔧 **Code Quality Rules**

### Error Handling
- Always handle async operations with try/catch
- Use error boundaries for component errors
- Provide meaningful error messages
- Log errors appropriately

```typescript
// ✅ Good error handling
const handleSubmit = async () => {
  try {
    setLoading(true);
    await submitData(formData);
  } catch (error) {
    console.error('Submit failed:', error);
    setError('Failed to submit. Please try again.');
  } finally {
    setLoading(false);
  }
};
```

### Accessibility
- Use semantic HTML elements
- Include ARIA labels where needed
- Ensure keyboard navigation works
- Test with screen readers
- Include testID props for important elements

```typescript
// ✅ Mobile accessibility patterns
<Button
  accessible={true}
  accessibilityLabel="Submit form"
  accessibilityHint="Submits the current form data"
  accessibilityRole="button"
  onPress={handleSubmit}
/>
```

### Security Guidelines
1. **NEVER** commit sensitive data
2. **USE** environment variables for API keys
3. **VALIDATE** all user inputs
4. **USE** secure storage for sensitive data

```typescript
// ✅ Secure storage for mobile
import { MMKV } from "react-native-mmkv"

const secureStorage = new MMKV({
  id: "secure-storage",
  encryptionKey: "your-encryption-key"
})

secureStorage.set("user.token", authToken)
```

## 🚀 **Performance Best Practices**

### Optimization Guidelines
1. **USE** `observer` for all components accessing stores
2. **USE** `useCallback` for event handlers in lists
3. **USE** `useMemo` for expensive calculations
4. **PREFER** `FlashList` over `FlatList` for large lists
5. **LAZY LOAD** heavy screens and components

### Mobile List Performance
```typescript
import { FlashList } from "@shopify/flash-list"

// ✅ Use FlashList for large datasets
<FlashList
  data={items}
  renderItem={renderItem}
  estimatedItemSize={60}
  keyExtractor={(item) => item.id}
  removeClippedSubviews={true}
  maxToRenderPerBatch={10}
  windowSize={10}
/>
```

### Image Optimization
```typescript
// ✅ Use optimized image component
<AutoImage
  source={{ uri: imageUrl }}
  style={$imageStyle}
  resizeMode="cover"
  // Automatic loading states
/>
```

### Memory Management
```typescript
// ✅ Cleanup in useEffect
useEffect(() => {
  const subscription = someService.subscribe()
  
  return () => {
    subscription.unsubscribe()
  }
}, [])
```

## 🧪 **Testing Guidelines**

### Test Structure
```typescript
import { render, screen } from "@testing-library/react-native"
import { ComponentName } from "./ComponentName"

describe("ComponentName", () => {
  it("should render correctly", () => {
    render(<ComponentName />)
    expect(screen.getByText("Expected Text")).toBeTruthy()
  })
})
```

### Testing Rules
1. **USE** React Native Testing Library
2. **INCLUDE** testID props for important elements
3. **TEST** user interactions and state changes
4. **USE** Maestro for E2E tests

### E2E Testing with Maestro
```yaml
# .maestro/test-flow.yaml
appId: com.padelcommunityapp
---
- launchApp
- tapOn: "Login Button"
- inputText: "<EMAIL>"
- tapOn: "Submit"
- assertVisible: "Welcome Screen"
```

## 📦 **Asset Management Best Practices**

### Asset Organization
```
assets/
├── icons/          # App icons and small graphics
├── images/         # Larger images and photos
└── fonts/          # Custom fonts
```

### Asset Guidelines
1. **USE** `require()` for local assets
2. **PROVIDE** multiple resolutions (@2x, @3x)
3. **OPTIMIZE** images for mobile
4. **USE** vector formats when possible

## 📋 **Code Review Checklist**

### Before Submitting
- [ ] Component is under size limits
- [ ] Types are properly defined
- [ ] No prop drilling beyond 2 levels
- [ ] Error handling is implemented
- [ ] Performance optimizations applied where needed
- [ ] Tests are written and passing
- [ ] Accessibility requirements met
- [ ] No console.logs in production code
- [ ] Safe areas properly handled (mobile)
- [ ] Touch targets meet minimum requirements (mobile)

### Refactoring Triggers
- Component exceeds 300 lines
- Function exceeds 50 lines
- Prop drilling beyond 2-3 levels
- Duplicate code in multiple places
- Complex state management in component
- Performance issues identified

## ⚡ **Quick Reference**

### 🏆 **Use These Existing Patterns FIRST**
- **Theming**: `const { themed } = useAppTheme()` → `app/utils/useAppTheme.ts`
- **Icons**: `<Icon icon="back" />` → `app/components/Icon.tsx`
- **Buttons**: `<Button preset="filled" />` → `app/components/Button.tsx`
- **Text**: `<Text preset="heading" />` → `app/components/Text.tsx`
- **Screens**: `<Screen preset="scroll" />` → `app/components/Screen.tsx`
- **Navigation**: `AppStackScreenProps<"Screen">` → `app/navigators/`
- **State**: `const { authStore } = useStores()` → `app/models/`
- **i18n**: `translate("key")` → `app/i18n/`
- **Safe Areas**: `useSafeAreaInsetsStyle(["top"])` → `app/utils/`

### Component Size Red Flags
- 🚨 **> 500 lines**: Immediate refactoring needed
- ⚠️ **300-500 lines**: Plan refactoring
- ✅ **< 300 lines**: Good size

### Extraction Patterns
- **Custom Hook**: `const { data, loading, error } = useFeature()`
- **Sub-component**: `<FeatureSection {...props} />`
- **Utility**: `const result = processData(input)`
- **Constant**: `export const FEATURE_CONFIG = { ... }`

---

*Follow these best practices to maintain clean, scalable, and maintainable code in the Padel Community Connect app.*