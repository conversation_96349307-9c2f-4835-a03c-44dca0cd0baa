# Product Overview

**Padel Community App** is a React Native mobile application for managing padel sports communities. The app enables users to:

- Join and manage padel communities
- Create and participate in events/matches
- View community member profiles and leaderboards
- Receive notifications and chat with other members
- Track game results and trophies

The app supports both public communities (open join) and private communities (request-based membership). Users can create events, track scores, and maintain their player profiles within their communities.

## Key Features
- Community management (join, create, browse)
- Event scheduling and participation
- Real-time messaging and notifications
- User profiles with trophy/achievement system
- Leaderboards and statistics
- Multi-language support (i18n)
- Deep linking support for password resets and navigation