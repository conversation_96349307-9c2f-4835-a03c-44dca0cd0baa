# Project Structure

## Root Directory
```
├── app/                    # Main application code
├── assets/                 # Static assets (images, icons)
├── docs/                   # Project documentation
├── android/                # Android native code
├── ios/                    # iOS native code
├── ignite/                 # Ignite CLI templates
└── .kiro/                  # Kiro steering rules
```

## App Directory Structure (`/app`)

### Core Architecture
```
app/
├── app.tsx                 # App entry point
├── components/             # Reusable UI components
├── screens/               # Screen components
├── navigators/            # Navigation configuration
├── models/                # MobX-State-Tree stores
├── services/              # Business logic services
├── hooks/                 # Custom React hooks
├── utils/                 # Utility functions
├── theme/                 # Design system (colors, typography, spacing)
├── i18n/                  # Internationalization
├── types/                 # TypeScript type definitions
├── config/                # App configuration
└── data/                  # Mock/static data
```

## Key Conventions

### Components (`/app/components`)
- Export from `index.ts` for clean imports
- Use PascalCase for component names
- Include TypeScript interfaces for props
- Follow atomic design principles where applicable

### Screens (`/app/screens`)
- Each screen has its own folder with:
  - `ScreenName.tsx` - Main screen component
  - `index.ts` - Export file
  - `components/` - Screen-specific components (if needed)
  - `hooks/` - Screen-specific hooks (if needed)
  - `types.ts` - Screen-specific types
  - `utils.ts` - Screen-specific utilities

### Services (`/app/services`)
- Static classes with business logic methods
- Separate concerns from UI components
- Handle API calls, data transformation, validation
- Example: `CommunityService.ts`, `EventService.ts`

### Models (`/app/models`)
- MobX-State-Tree store definitions
- `RootStore.ts` combines all stores
- `helpers/` contains store utilities
- Use `observer` HOC for reactive components

### Navigation (`/app/navigators`)
- `AppNavigator.tsx` - Main navigation stack
- `AppStackParamList` type defines all routes
- Conditional rendering based on authentication state

### Theme (`/app/theme`)
- `colors.ts` - Color palette and semantic colors
- `typography.ts` - Font definitions
- `spacing.ts` - Consistent spacing values
- `styles.ts` - Common style utilities

### Hooks (`/app/hooks`)
- Custom hooks for reusable logic
- Screen-specific hooks in screen folders
- Follow `use` prefix convention

## File Naming Conventions
- **Components**: PascalCase (`Button.tsx`, `CommunityCard.tsx`)
- **Screens**: PascalCase with "Screen" suffix (`LoginScreen.tsx`)
- **Services**: PascalCase with "Service" suffix (`CommunityService.ts`)
- **Hooks**: camelCase with "use" prefix (`useEvents.ts`)
- **Types**: camelCase (`communities.ts`, `events.ts`)
- **Utils**: camelCase (`formatDate.ts`, `avatar.ts`)

## Import Conventions
- Use path aliases: `@/` for app directory
- Barrel exports from `index.ts` files
- Group imports: external libraries, internal modules, relative imports

## Architecture Patterns
- **Service Layer**: Business logic separated from UI
- **Custom Hooks**: Screen logic extraction for reusability
- **Observer Pattern**: MobX reactive components
- **Composition**: Prefer composition over inheritance
- **Separation of Concerns**: Clear boundaries between UI, logic, and data