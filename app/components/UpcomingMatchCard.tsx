import { FC } from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text } from "./Text"
import { StatusBadge } from "./StatusBadge"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { Icon } from "./Icon"
import { Button } from "./Button"

export interface UpcomingMatch {
  id: number
  title: string
  date: string
  timestamp?: string
  location: string
  status: string
}

export interface UpcomingMatchCardProps {
  match: UpcomingMatch
  onPress?: (match: UpcomingMatch) => void
}

export const UpcomingMatchCard: FC<UpcomingMatchCardProps> = ({ match, onPress }) => {
  const { themed } = useAppTheme()

  const handlePress = () => {
    if (onPress) {
      onPress(match)
    }
  }

  // Parse the timestamp to extract month and day
  const parseDate = (match: UpcomingMatch) => {
    // Use timestamp if available, otherwise fallback to current date
    const dateString = match.timestamp || new Date().toISOString()
    const date = new Date(dateString)
    const month = date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase()
    const day = date.getDate().toString().padStart(2, '0')
    return { month, day }
  }

  const { month, day } = parseDate(match)

  // Prepare all styles outside JSX
  const $cardStyle = themed($matchCard)
  const $containerRowStyle = themed($containerRow)
  const $dateBoxStyle = themed($dateBox)
  const $dateMonthStyle = themed($dateMonth)
  const $dateDayStyle = themed($dateDay)
  const $mainContentStyle = themed($mainContent)
  const $eventTitleStyle = themed($eventTitle)
  const $eventLocationStyle = themed($eventLocation)
  const $locationRowStyle = themed($locationRow)
  const $arrowButtonStyle = themed($arrowButton)
  const $arrowIconStyle = themed($arrowIcon)

  const handleArrowPress = (e: any) => {
    e.stopPropagation && e.stopPropagation();
    if (onPress) {
      onPress(match)
    }
  }

  return (
    <TouchableOpacity
      style={$cardStyle}
      onPress={handlePress}
      activeOpacity={0.9}
    >
      <View style={$containerRowStyle}>
        <View style={$dateBoxStyle}>
          <Text text={month} style={$dateMonthStyle} />
          <Text text={day} style={$dateDayStyle} />
        </View>
        <View style={$mainContentStyle}>
          <Text text={match.title} style={$eventTitleStyle} />
          <View style={$locationRowStyle}>
            <Icon icon="location" size={16} color="#666" />
            <Text text={match.location} style={$eventLocationStyle} />
          </View>
        </View>
        <TouchableOpacity style={$arrowButtonStyle} onPress={handleArrowPress} activeOpacity={0.7}>
          <Icon icon="caretRight" size={22} color="#111" containerStyle={$arrowIconStyle} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  )
}

const $matchCard: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "#fff7ed", // very soft blue, almost transparent
  borderRadius: spacing.sm,
  padding: spacing.sm,
  marginBottom: spacing.xs,
})

const $containerRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
})

const $dateBox: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "#f97315", // teal/green color like in the design
  borderRadius: spacing.xs,
  width: 50,
  height: 50,
  justifyContent: "center",
  alignItems: "center",
  marginRight: spacing.sm,
})

const $dateMonth: ThemedStyle<TextStyle> = () => ({
  color: "#fff",
  fontSize: 12,
  fontWeight: "600",
  lineHeight: 14,
})

const $dateDay: ThemedStyle<TextStyle> = () => ({
  color: "#fff",
  fontSize: 16,
  fontWeight: "700",
  lineHeight: 18,
})

const $mainContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  alignItems: "flex-start",
  justifyContent: "center",
})

const $eventTitle: ThemedStyle<TextStyle> = ({ spacing }) => ({
  color: "#111",
  fontSize: 16,
  fontWeight: "600",
  marginBottom: spacing.xxs,
})

const $locationRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xxs,
})

const $eventLocation: ThemedStyle<TextStyle> = () => ({
  color: "#666",
  fontSize: 14,
  fontWeight: "400",
})

const $arrowButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 36,
  height: 36,
  borderRadius: 18,
  backgroundColor: "#e5e7eb",
  justifyContent: "center",
  alignItems: "center",
  marginLeft: spacing.sm,
})

const $arrowIcon: ThemedStyle<ViewStyle> = () => ({})
