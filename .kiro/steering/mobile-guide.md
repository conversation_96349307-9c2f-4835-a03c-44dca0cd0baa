# Mobile Implementation Guide

## Overview
This guide focuses on **mobile-specific implementation details** for our Ignite-based React Native application. For architectural patterns and general best practices, see the related guides.

## 📱 **Device Feature Integration**

### 🔒 **Storage (Secure & Fast)**
```typescript
import { MMKV } from "react-native-mmkv"

// Fast, secure storage for mobile
const storage = new MMKV()
storage.set("user.token", authToken)
const token = storage.getString("user.token")

// Encrypted storage for sensitive data
const secureStorage = new MMKV({
  id: "secure-storage",
  encryptionKey: "your-encryption-key"
})
```

### 📷 **Camera Integration**
```typescript
import { Camera } from "expo-camera"

const [hasPermission, setHasPermission] = useState<boolean | null>(null)

useEffect(() => {
  (async () => {
    const { status } = await Camera.requestCameraPermissionsAsync()
    setHasPermission(status === "granted")
  })()
}, [])

// Camera component usage
<Camera style={$cameraStyle} type={cameraType}>
  <View style={$buttonContainer}>
    <TouchableOpacity style={$button} onPress={takePicture}>
      <Text style={$text}>Take Photo</Text>
    </TouchableOpacity>
  </View>
</Camera>
```

### 📍 **Location Services**
```typescript
import * as Location from "expo-location"

const getCurrentLocation = async () => {
  const { status } = await Location.requestForegroundPermissionsAsync()
  if (status === "granted") {
    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.High,
    })
    return location
  }
  throw new Error("Location permission denied")
}

// Background location (for fitness tracking)
const startLocationUpdates = async () => {
  await Location.startLocationUpdatesAsync("BACKGROUND_LOCATION_TASK", {
    accuracy: Location.Accuracy.Balanced,
    timeInterval: 5000,
    distanceInterval: 10,
  })
}
```

### 🔔 **Push Notifications**
```typescript
import * as Notifications from "expo-notifications"
import * as Device from "expo-device"

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
})

// Register for push notifications
const registerForPushNotificationsAsync = async () => {
  let token;
  
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    if (finalStatus !== 'granted') {
      alert('Failed to get push token for push notification!');
      return;
    }
    token = (await Notifications.getExpoPushTokenAsync()).data;
  }

  return token;
}
```

## 🌐 **Network & Offline Handling**

### Network State Detection
```typescript
import NetInfo from "@react-native-community/netinfo"

const [isConnected, setIsConnected] = useState<boolean>(true)
const [connectionType, setConnectionType] = useState<string>('unknown')

useEffect(() => {
  const unsubscribe = NetInfo.addEventListener(state => {
    setIsConnected(state.isConnected ?? false)
    setConnectionType(state.type)
  })
  
  return unsubscribe
}, [])

// Show offline banner
if (!isConnected) {
  return <OfflineBanner />
}
```

### Offline Storage Strategy
```typescript
// Cache data for offline access
const cacheData = (key: string, data: any) => {
  const cacheKey = `cache.${key}`
  const cacheItem = {
    data,
    timestamp: Date.now(),
    version: "1.0"
  }
  storage.set(cacheKey, JSON.stringify(cacheItem))
}

const getCachedData = (key: string, maxAge: number = 3600000) => { // 1 hour default
  const cached = storage.getString(`cache.${key}`)
  if (cached) {
    const cacheItem = JSON.parse(cached)
    const isExpired = Date.now() - cacheItem.timestamp > maxAge
    return isExpired ? null : cacheItem.data
  }
  return null
}

// Sync when connection restored
const syncOfflineData = async () => {
  const offlineActions = getCachedData("offline_actions") || []
  for (const action of offlineActions) {
    try {
      await syncAction(action)
    } catch (error) {
      // Handle sync failures
    }
  }
  storage.delete("cache.offline_actions")
}
```

## 🎨 **Platform-Specific Patterns**

### iOS Specific Features
```typescript
import { Platform } from "react-native"

// iOS-specific styling
const $iosStyle: ThemedStyle<ViewStyle> = ({ colors }) => ({
  ...Platform.select({
    ios: {
      shadowColor: colors.palette.neutral800,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    android: {
      elevation: 4,
    },
  }),
})

// iOS specific haptic feedback
import * as Haptics from "expo-haptics"

const handleButtonPress = () => {
  if (Platform.OS === 'ios') {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)
  }
  // Button action
}
```

### Android Specific Features
```typescript
import { BackHandler } from "react-native"
import { useBackButtonHandler } from "@/utils/useBackButtonHandler"

// Android back button handling
useBackButtonHandler((routeName) => {
  if (routeName === "Home") {
    // Show exit confirmation
    Alert.alert(
      "Exit App",
      "Are you sure you want to exit?",
      [
        { text: "Cancel", style: "cancel" },
        { text: "Exit", onPress: () => BackHandler.exitApp() }
      ]
    )
    return true // Prevent default back behavior
  }
  return false // Allow default back behavior
})

// Android-specific permissions
import { PermissionsAndroid } from "react-native"

const requestStoragePermission = async () => {
  if (Platform.OS === 'android') {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE
    )
    return granted === PermissionsAndroid.RESULTS.GRANTED
  }
  return true
}
```

## 📐 **Responsive Design Patterns**

### Screen Size Adaptation
```typescript
import { Dimensions, useWindowDimensions } from "react-native"

const { width: screenWidth, height: screenHeight } = useWindowDimensions()

const $responsiveStyle: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: screenWidth > 768 ? spacing.lg : spacing.sm,
  flexDirection: screenWidth > 768 ? "row" : "column",
  
  // Tablet-specific layouts
  ...(screenWidth > 768 && {
    maxWidth: 800,
    alignSelf: "center",
  }),
})
```

### Orientation Handling
```typescript
import { useDeviceOrientation } from "@react-native-community/hooks"

const orientation = useDeviceOrientation()
const isLandscape = orientation.landscape

const $orientationStyle: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: isLandscape ? "row" : "column",
  padding: isLandscape ? spacing.lg : spacing.md,
})

// Lock orientation for specific screens
import * as ScreenOrientation from "expo-screen-orientation"

useEffect(() => {
  // Lock to portrait for login screen
  ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP)
  
  return () => {
    // Unlock on cleanup
    ScreenOrientation.unlockAsync()
  }
}, [])
```

## ⌨️ **Keyboard Handling**

### Keyboard Avoidance
```typescript
import { KeyboardProvider } from "react-native-keyboard-controller"
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view"

// Form with keyboard avoidance
<KeyboardAwareScrollView
  style={$container}
  resetScrollToCoords={{ x: 0, y: 0 }}
  contentContainerStyle={$contentContainer}
  scrollEnabled={true}
  enableOnAndroid={true}
  enableAutomaticScroll={true}
  extraHeight={100}
>
  <TextField placeholder="Email" />
  <TextField placeholder="Password" secureTextEntry />
  <Button text="Login" />
</KeyboardAwareScrollView>
```

### Dynamic Keyboard Handling
```typescript
import { useKeyboard } from "@react-native-community/hooks"

const keyboard = useKeyboard()

const $dynamicStyle: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingBottom: keyboard.keyboardShown ? keyboard.keyboardHeight + spacing.md : spacing.md,
})
```

## 🎬 **Animation Patterns**

### Performance Animations with Reanimated
```typescript
import Animated, { 
  useSharedValue, 
  withSpring, 
  useAnimatedStyle,
  withTiming,
  runOnJS
} from "react-native-reanimated"

const scale = useSharedValue(1)
const opacity = useSharedValue(1)

const animatedStyle = useAnimatedStyle(() => ({
  transform: [{ scale: scale.value }],
  opacity: opacity.value,
}))

const handlePress = () => {
  scale.value = withSpring(1.1, {}, () => {
    scale.value = withSpring(1)
  })
}

// Gesture animations
import { Gesture, GestureDetector } from "react-native-gesture-handler"

const panGesture = Gesture.Pan()
  .onUpdate((event) => {
    translateX.value = event.translationX
    translateY.value = event.translationY
  })
  .onEnd(() => {
    translateX.value = withSpring(0)
    translateY.value = withSpring(0)
  })
```

## 🔒 **Security Implementation**

### Biometric Authentication
```typescript
import * as LocalAuthentication from "expo-local-authentication"

const authenticateWithBiometrics = async () => {
  // Check if biometric auth is available
  const hasHardware = await LocalAuthentication.hasHardwareAsync()
  const isEnrolled = await LocalAuthentication.isEnrolledAsync()
  
  if (hasHardware && isEnrolled) {
    const result = await LocalAuthentication.authenticateAsync({
      promptMessage: "Authenticate to access your account",
      fallbackLabel: "Use Passcode",
    })
    
    return result.success
  }
  
  return false
}
```

## 🔄 **App State & Lifecycle**

### App State Management
```typescript
import { AppState } from "react-native"

useEffect(() => {
  const handleAppStateChange = (nextAppState: string) => {
    if (nextAppState === 'active') {
      // App came to foreground
      refreshData()
    } else if (nextAppState === 'background') {
      // App went to background
      saveState()
    }
  }

  const subscription = AppState.addEventListener('change', handleAppStateChange)
  return () => subscription?.remove()
}, [])
```

### Deep Linking
```typescript
import * as Linking from "expo-linking"

const linking = {
  prefixes: [Linking.createURL("/")],
  config: {
    screens: {
      Home: "",
      Profile: "profile/:userId",
      Community: "community/:communityId",
      Event: "event/:eventId",
    },
  },
}

// Handle incoming links
useEffect(() => {
  const handleDeepLink = (url: string) => {
    // Parse and navigate to the appropriate screen
    const { hostname, path, queryParams } = Linking.parse(url)
    // Navigation logic
  }

  const subscription = Linking.addEventListener('url', handleDeepLink)
  return () => subscription?.remove()
}, [])
```

## ⚡ **Quick Mobile Implementation Reference**

### Essential Device APIs
```typescript
// Core device features
import * as Device from "expo-device"
import * as Application from "expo-application"
import * as Network from "expo-network"

const deviceInfo = {
  deviceName: Device.deviceName,
  osVersion: Device.osVersion,
  appVersion: Application.nativeApplicationVersion,
  buildVersion: Application.nativeBuildVersion,
}
```

### Common Mobile Patterns
```typescript
// Pull-to-refresh
<ScrollView
  refreshControl={
    <RefreshControl
      refreshing={isRefreshing}
      onRefresh={handleRefresh}
      tintColor={colors.tint}
    />
  }
>

// Swipe gestures
const swipeGesture = Gesture.Fling()
  .direction(Directions.RIGHT)
  .onEnd(() => {
    runOnJS(handleSwipeRight)()
  })

// Loading states
{isLoading ? (
  <ActivityIndicator size="large" color={colors.tint} />
) : (
  <ContentComponent />
)}
```

## 📋 **Mobile Development Workflow**

### Testing on Devices
```bash
# Development builds
npx expo run:ios --device    # Physical iOS device
npx expo run:android --device # Physical Android device

# Expo Go testing
npx expo start --tunnel      # External network access
```

### Build Variants
```bash
# Development build with debugging
eas build --profile development --platform ios

# Preview build for testing
eas build --profile preview --platform all

# Production build
eas build --profile production --platform all
```

---

*This guide focuses on mobile-specific implementation details. For architectural patterns, see coding-rules.md. For general best practices, see best-practices.md.*