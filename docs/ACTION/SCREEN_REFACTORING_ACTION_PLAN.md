# Padel Community App Screen Refactoring Action Plan

## 🎯 **Objective**
Refactor the `app/screens` directory to improve code organization, maintainability, and consistency across all screen components while adhering to the established architectural patterns.

**Context:** The current screen structure shows inconsistent patterns, with some screens following excellent separation of concerns (Communities, Profile, EventDetail) while others like DemoDebugScreen are monolithic and violate multiple architectural principles.

---

## 📊 **Current Assessment: 6/10**

### ✅ **Strong Areas**
- **Excellent hook separation** in Communities, Profile, and EventDetail screens ✅
- **Proper business logic separation** using dedicated hooks ✅
- **Consistent navigation patterns** with typed screen props ✅
- **Good component composition** with sub-components in directories ✅
- **Proper use of themed styles** across screens ✅

### ⚠️ **Areas for Improvement**
- **DemoDebugScreen** is 200+ lines monolithic component violating SRP
- **Inconsistent file naming** across screen directories
- **Missing service layer** for business logic in DemoDebugScreen
- **No custom hooks** for DemoDebugScreen functionality
- **Mixed responsibilities** in single components

### 🔍 **Technical Debt**
- **DemoDebugScreen.tsx**: 200+ lines, mixed data/logic/UI concerns
- **Missing index.ts exports** in some screen directories
- **Inconsistent hook patterns** between screens
- **No service layer** for debug/utility functions
- **Hardcoded strings** instead of translation keys

---

## 🚀 **Action Items**

### **Phase 1: DemoDebugScreen Refactoring**
**Priority: HIGH**

#### 1.1 Extract Business Logic to Service Layer
**Description:** Move all business logic from DemoDebugScreen to dedicated services

```typescript
// Current implementation (in component)
const demoReactotron = useMemo(
  () => async () => {
    if (__DEV__) {
      console.tron.display({
        name: "DISPLAY",
        value: {
          appId: Application.applicationId,
          appName: Application.applicationName,
          // ... more properties
        },
        important: true,
      })
    }
  },
  [],
)

// Target implementation (in service)
// app/services/DebugService.ts
export class DebugService {
  static async displayAppInfoInReactotron() {
    if (!__DEV__) return
    
    console.tron.display({
      name: "DISPLAY",
      value: {
        appId: Application.applicationId,
        appName: Application.applicationName,
        appVersion: Application.nativeApplicationVersion,
        appBuildVersion: Application.nativeBuildVersion,
        hermesEnabled: DebugService.isHermesEnabled(),
        fabricEnabled: DebugService.isFabricEnabled(),
      },
      important: true,
    })
  }

  static isHermesEnabled(): boolean {
    return typeof HermesInternal === "object" && HermesInternal !== null
  }

  static isFabricEnabled(): boolean {
    // @ts-expect-error
    return global.nativeFabricUIManager != null
  }

  static openLinkInBrowser(url: string): Promise<void> {
    return Linking.canOpenURL(url).then((canOpen) => {
      if (canOpen) Linking.openURL(url)
    })
  }
}
```

**TASKS:**
- [ ] Create `app/services/DebugService.ts` with static methods
- [ ] Extract all business logic from DemoDebugScreen
- [ ] Add comprehensive unit tests for DebugService
- [ ] Update DemoDebugScreen to use DebugService

**Acceptance Criteria:**
- [ ] All business logic removed from DemoDebugScreen
- [ ] DebugService has 100% test coverage
- [ ] DemoDebugScreen is under 100 lines
- [ ] No direct API calls in component

#### 1.2 Create Custom Hooks for DemoDebugScreen
**Description:** Extract UI state and effects into dedicated hooks following established patterns

```typescript
// app/screens/DemoDebugScreen/hooks/useDebugData.ts
export const useDebugData = () => {
  const { authenticationStore } = useStores()
  
  return {
    appInfo: {
      appId: Application.applicationId,
      appName: Application.applicationName,
      appVersion: Application.nativeApplicationVersion,
      appBuildVersion: Application.nativeBuildVersion,
    },
    runtimeInfo: {
      hermesEnabled: DebugService.isHermesEnabled(),
      fabricEnabled: DebugService.isFabricEnabled(),
    },
    logout: authenticationStore.logout,
  }
}

// app/screens/DemoDebugScreen/hooks/useDebugActions.ts
export const useDebugActions = () => {
  const { setThemeContextOverride } = useAppTheme()
  const colorScheme = useColorScheme()
  
  const toggleTheme = useCallback(() => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
    setThemeContextOverride(prev => prev === "dark" ? "light" : "dark")
  }, [setThemeContextOverride])
  
  const resetTheme = useCallback(() => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
    setThemeContextOverride(undefined)
  }, [setThemeContextOverride])
  
  const showReactotronDisplay = useCallback(() => {
    DebugService.displayAppInfoInReactotron()
  }, [])
  
  return {
    toggleTheme,
    resetTheme,
    showReactotronDisplay,
    openIssuesLink: () => DebugService.openLinkInBrowser("https://github.com/infinitered/ignite/issues"),
  }
}
```

**Steps:**
1. Create `app/screens/DemoDebugScreen/hooks/` directory
2. Create `useDebugData.ts`, `useDebugActions.ts`, and `useDebugNavigation.ts`
3. Implement hooks following established patterns
4. Update DemoDebugScreen to use new hooks

**Dependencies:** Phase 1.1 completion

#### 1.3 Extract Sub-components for DemoDebugScreen
**Description:** Break down DemoDebugScreen into smaller, reusable components

```typescript
// app/screens/DemoDebugScreen/components/DebugInfoList.tsx
interface DebugInfoListProps {
  appInfo: {
    appId: string
    appName: string
    appVersion: string | null
    appBuildVersion: string | null
  }
  runtimeInfo: {
    hermesEnabled: boolean
    fabricEnabled: boolean
  }
}

// app/screens/DemoDebugScreen/components/ThemeControls.tsx
interface ThemeControlsProps {
  currentTheme: string | undefined
  systemTheme: string | null | undefined
  onToggleTheme: () => void
  onResetTheme: () => void
}

// app/screens/DemoDebugScreen/components/DebugActions.tsx
interface DebugActionsProps {
  onShowReactotron: () => void
  onLogout: () => void
  onOpenIssues: () => void
}
```

**Risk Assessment:**
- **High Risk:** Breaking existing functionality during refactor
- **Medium Risk:** Missing edge cases in new service layer
- **Mitigation:** Comprehensive testing and gradual rollout

### **Phase 2: Standardize Screen Directory Structure**
**Priority: MEDIUM**

#### 2.1 Ensure Consistent Directory Structure
**Description:** Standardize all screen directories to follow the established pattern

**Current Structure (Good):**
```
Communities/
├── CommunitiesScreen.tsx
├── components/
│   ├── CommunitiesHeader.tsx
│   └── CommunitiesList.tsx
├── hooks/
│   ├── useCommunitiesData.ts
│   ├── useCommunitiesActions.ts
│   └── index.ts
├── types.ts
├── utils.ts
└── index.ts
```

**Target Structure for All Screens:**
```
[ScreenName]/
├── [ScreenName]Screen.tsx          # Main screen component (< 150 lines)
├── components/                     # Screen-specific components
│   ├── index.ts                   # Barrel exports
│   └── *.tsx                      # Individual components
├── hooks/                         # Custom hooks
│   ├── index.ts                   # Barrel exports
│   ├── use[Screen]Data.ts         # Data fetching/state
│   ├── use[Screen]Actions.ts      # Business logic
│   ├── use[Screen]Navigation.ts   # Navigation logic
│   └── use[Screen]UIActions.ts    # UI state management
├── types.ts                       # Screen-specific types
├── utils.ts                       # Screen utilities
├── services/                      # Screen-specific services (if needed)
└── index.ts                       # Public exports
```

**TASKS:**
- [ ] Audit all screen directories for consistency
- [ ] Create missing directories and files
- [ ] Move components to appropriate subdirectories
- [ ] Update all import paths
- [ ] Ensure all screens have proper index.ts exports

#### 2.2 Extract Common Patterns to Shared Utilities
**Description:** Create shared utilities for common screen patterns

```typescript
// app/utils/screenHelpers.ts
export const createScreenHooks = <T extends Record<string, any>>(hooks: T) => hooks

export const useScreenHeader = (config: HeaderConfig) => {
  useHeader(config)
}

// app/utils/navigationHelpers.ts
export const createNavigationHooks = <T extends Record<string, any>>(navigation: any) => ({
  goBack: () => navigation.goBack(),
  navigate: (screen: string, params?: any) => navigation.navigate(screen, params),
})

// app/services/ScreenService.ts
export class ScreenService {
  static createScrollManager() {
    let scrollRef: ScrollView | null = null
    
    return {
      setRef: (ref: ScrollView) => { scrollRef = ref },
      scrollToTop: () => scrollRef?.scrollTo({ y: 0, animated: true }),
    }
  }
}
```

### **Phase 3: Performance Optimization**
**Priority: MEDIUM**

#### 3.1 Implement Lazy Loading for Screens
**Description:** Use React.lazy for code splitting and improved performance

```typescript
// Update app/screens/index.ts
export const DemoDebugScreen = lazy(() => import("./DemoDebugScreen/DemoDebugScreen"))
export const CommunitiesScreen = lazy(() => import("./Communities/CommunitiesScreen"))
// ... etc
```

#### 3.2 Optimize Bundle Size
**Description:** Analyze and optimize screen bundle sizes

**Steps:**
1. Run bundle analysis
2. Identify large dependencies
3. Implement dynamic imports
4. Optimize image assets

---

## 🔧 **Implementation Guidelines**

### **Code Standards**
```typescript
// Screen component pattern
interface ScreenProps extends AppStackScreenProps<"ScreenName"> {}

export const ScreenName: FC<ScreenProps> = observer(function ScreenName(props) {
  // Use hooks for all logic
  const { data, loading, error } = useScreenData()
  const actions = useScreenActions()
  const navigation = useScreenNavigation(props.navigation)
  
  // Minimal JSX
  return (
    <Screen preset="fixed">
      <ScreenContent {...{ data, loading, error, ...actions, ...navigation }} />
    </Screen>
  )
})

// Hook pattern
export const useScreenData = () => {
  // Data fetching logic
  return { data, loading, error }
}

export const useScreenActions = () => {
  // Business logic
  return { handleAction1, handleAction2 }
}
```

### **File Structure**
```
app/screens/
├── [ScreenName]/
│   ├── [ScreenName]Screen.tsx          # Main screen (< 150 lines)
│   ├── components/
│   │   ├── index.ts                   # Barrel exports
│   │   └── *.tsx                      # UI components
│   ├── hooks/
│   │   ├── index.ts                   # Barrel exports
│   │   ├── use[Screen]Data.ts         # Data
│   │   ├── use[Screen]Actions.ts      # Business logic
│   │   ├── use[Screen]Navigation.ts   # Navigation
│   │   └── use[Screen]UIActions.ts    # UI state
│   ├── types.ts                       # Screen types
│   ├── utils.ts                       # Utilities
│   └── index.ts                       # Public API
```

### **Testing Strategy**
- **Unit Tests:** Test each hook and service independently
- **Integration Tests:** Test screen interactions
- **E2E Tests:** Test complete user flows

### **Performance Considerations**
- Keep screen components under 150 lines
- Use React.memo for expensive components
- Implement proper loading states
- Use FlashList for large lists

### **Security Considerations**
- Validate all navigation parameters
- Sanitize user inputs
- Use secure storage for sensitive data

---

## 🔄 **Migration Strategy**

### **Incremental Approach**
1. **Week 1:** Refactor DemoDebugScreen as proof of concept
2. **Week 2:** Standardize top 5 most used screens
3. **Week 3:** Complete remaining screens
4. **Week 4:** Performance optimization and testing

### **Rollback Plan**
- **Trigger Conditions:** Critical bugs in production
- **Rollback Process:** Revert to previous commit
- **Recovery Time:** < 30 minutes

### **Testing Strategy**
- **Development Testing:** Jest unit tests for all new code
- **Staging Testing:** Full regression testing
- **Production Testing:** A/B testing with feature flags

---

### **Critical Path**
- DemoDebugScreen refactoring (Phase 1)
- CommunitiesScreen validation (Phase 2)
- Performance testing (Phase 3)

---

## 🛠 **Resources & Dependencies**

### **Technical Dependencies**
- React.lazy for code splitting
- Jest for testing
- React Native Testing Library
- Bundle analyzer

### **External Dependencies**
- None - all refactoring uses existing stack

---

## 🚨 **Risk Assessment**

### **High Risk Items**
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Breaking existing functionality | High | Medium | Comprehensive testing + gradual rollout |
| Performance regression | High | Low | Performance testing + monitoring |
| Missing edge cases | Medium | Medium | Code review + QA testing |

### **Medium Risk Items**
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Import path issues | Low | Medium | Automated import checking |
| Bundle size increase | Medium | Low | Bundle analysis + optimization |

---

## ✅ **Definition of Done**

### **Feature Complete**
- [ ] All screens refactored to follow consistent patterns
- [ ] DemoDebugScreen under 100 lines
- [ ] All screens have proper directory structure
- [ ] All business logic extracted to services/hooks

### **Quality Assurance**
- [ ] All existing functionality preserved
- [ ] Unit tests for all new services/hooks
- [ ] Integration tests for screen interactions
- [ ] Performance benchmarks met

### **Documentation**
- [ ] Updated screen development guidelines
- [ ] Added component documentation
- [ ] Updated README with new patterns
- [ ] Added migration guide

### **Production Ready**
- [ ] All tests passing
- [ ] Performance validated
- [ ] Code review completed
- [ ] Stakeholder approval received

---

## 📚 **References & Resources**

### **Documentation**
- [coding-rules.mdc](mdc:coding-rules.mdc) - Core architectural patterns
- [best-practices.mdc](mdc:best-practices.mdc) - Code quality guidelines
- [mobile-guide.mdc](mdc:mobile-guide.mdc) - Mobile-specific patterns

### **Technical Resources**
- React Native Performance Best Practices
- MobX-State-Tree documentation
- React Navigation TypeScript guide

---

**Template Version:** 1.0  
**Created:** July 17, 2025  
**Last Updated:** July 17, 2025  
**Template Author:** Padel Community App Team
